<p>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" /><link
    href="https://fonts.googleapis.com/css2?family=Old+Standard+TT:ital,wght@0,400;0,700;1,400&amp;family=Fira+Code:wght@300;400;500;600;700&amp;display=swap"
    rel="stylesheet"
  />
</p>
<article class="editorial-article">
  <!-- Editorial Header -->
  <header class="article-header">
    <div class="header-meta">
      <span class="issue-number">EDITORIAL N°1</span>
      <span class="publication-date">SUMMER 2025</span>
    </div>
    <h1 class="article-title">
      <span style="color: rgb(255, 255, 255)"
        >5 Signs Your Skincare Isn't Helping with Aging</span
      >
    </h1>
    <div class="article-subtitle">The Truth About Aging Gracefully</div>
    <div class="byline">
      <span class="author">By Cathám Beauty Editorial</span>
      <span class="read-time">6 min read</span>
    </div>
  </header>

  <!-- Editorial Content -->
  <section class="editorial-content">
    <div class="content-grid">
      <div class="main-content">
        <p class="lead-paragraph">
          Aging. It's inevitable, but that doesn't mean we have to settle for
          tired, dull skin along the way.
        </p>
        <div class="editorial-text">
          <p>
            You may be using products that promise miracles, but when it comes
            to aging skin, it's not about quick fixes or empty promises—it's
            about strategies that actually work with your skin.
          </p>
          <p>
            <strong
              >So, how do you know if your skincare is actually helping with the
              aging process?</strong
            >
            Here are five signs it's time to reconsider what's in your skincare
            routine:
          </p>
        </div>
        <h2 class="section-heading">
          1. Your Skin Lacks Firmness and Elasticity
        </h2>
        <div class="editorial-text">
          <p>
            Loss of firmness is one of the most telling signs of aging skin. If
            your skin feels less bouncy and resilient than it used to, it's time
            to focus on ingredients that support collagen production and skin
            structure.
          </p>
          <p>
            This is where targeted treatments become essential. Look for serums
            and creams that contain <strong>peptides</strong>,
            <strong>bakuchiol</strong>, and
            <strong>vitamin C</strong>—ingredients that work together to rebuild
            your skin's foundation from within.
          </p>
        </div>
        <div class="magazine-grid-layout">
          <div class="magazine-grid-item wide">
            <img
              src="https://cdn.shopify.com/s/files/1/0909/6560/6735/files/pexels-photo-8626078.webp?v=1744366051"
              alt="Skin lacking firmness and elasticity"
              class="magazine-image"
            />
            <div class="magazine-overlay-text">
              <span class="overlay-number">01</span>
              <span class="overlay-title">Firmness & Elasticity</span>
              <p>Advanced collagen support for youthful skin structure</p>
            </div>
          </div>
        </div>
        <div class="product-showcase">
          <h3 class="showcase-title">Try This</h3>
          <div class="product-links">
            <a
              href="https://catham.eu/products/brightening-vitamin-c-serum"
              class="product-link"
              >Brightening Vitamin C Serum</a
            >
            <a
              href="https://catham.eu/products/niacinamide-gel-moisturiser"
              class="product-link"
              >Niacinamide Gel Moisturiser</a
            >
          </div>
        </div>
        <h2 class="section-heading">
          2. Uneven Skin Tone or Dark Spots Won't Budge
        </h2>
        <div class="editorial-text">
          <p>
            Aging skin tends to lose its even tone, with dark spots and
            hyperpigmentation becoming more noticeable. If your routine isn't
            tackling these concerns, it's time to switch gears.
          </p>
          <p>
            Ingredients like <strong>Niacinamide</strong> (Vitamin B3) can help
            fade dark spots and even out skin tone by regulating melanin
            production. Look for treatments that address both pigmentation and
            texture, helping you not just fade those spots, but also renew your
            skin at a cellular level.
          </p>
        </div>
        <div class="magazine-grid-layout">
          <div class="magazine-grid-item wide">
            <img
              src="https://cdn.shopify.com/s/files/1/0909/6560/6735/files/BlogImages-BrownSpotsDetail_8292df63-0a05-4c51-985d-2ef22a182c9c.jpg?v=1744365804"
              alt="Dark spots and hyperpigmentation"
              class="magazine-image"
            />
            <div class="magazine-overlay-text">
              <span class="overlay-number">02</span>
              <span class="overlay-title"
                >Targeting Hyperpigmentation with Precision</span
              >
              <p>
                Advanced ingredients work to fade dark spots and even skin tone
              </p>
            </div>
          </div>
        </div>
        <div class="product-showcase">
          <h3 class="showcase-title">Try This</h3>
          <div class="product-links">
            <a
              href="https://catham.eu/products/brightening-vitamin-c-serum"
              class="product-link"
              >Brightening Vitamin C Serum</a
            >
            <a
              href="https://catham.eu/products/niacinamide-gel-moisturiser"
              class="product-link"
              >Niacinamide Gel Moisturiser</a
            >
          </div>
        </div>
        <h2 class="section-heading">
          3. Your Skin Still Looks Dull and Lifeless
        </h2>
        <div class="editorial-text">
          <p>
            Tired of dull, lifeless skin that just won't glow? That's a major
            sign your routine is missing key ingredients that enhance radiance
            and help the skin regenerate. If your skin feels like it's stuck in
            a perpetual winter, it's time to add some game-changers into your
            routine.
          </p>
          <p>
            Ingredients like <strong>Vitamin C</strong> and
            <strong>Niacinamide (Vitamin B3)</strong> are powerful when it comes
            to brightening skin, boosting hydration, and getting that youthful
            glow back. You shouldn't need to rely on filters to make your skin
            shine.
          </p>
        </div>
        <div class="product-showcase">
          <h3 class="showcase-title">Try This</h3>
          <div class="product-links">
            <a
              href="https://catham.eu/products/brightening-vitamin-c-serum"
              class="product-link"
              >Brightening Vitamin C Serum</a
            >
            <a
              href="https://catham.eu/products/rich-collagen-protection-cream"
              class="product-link"
              >Rich Collagen Protection Cream</a
            >
          </div>
        </div>
        <h2 class="section-heading">
          4. Your Skin Feels Dry, Even After Moisturizing
        </h2>
        <div class="editorial-text">
          <p>
            Hydration isn't just about slathering on a moisturizer—it's about
            giving your skin the right balance of moisture and nourishment. If
            your skin still feels dry or tight after moisturizing, it's a sign
            your skincare is not properly addressing your skin's needs.
          </p>
          <p>
            <strong>Ceramides</strong> are essential for helping your skin
            retain moisture and rebuild its protective barrier. They're key to
            keeping your skin hydrated and preventing water loss, especially as
            you age. Without ceramides, your skin may struggle to keep moisture
            in, leading to that uncomfortable tightness.
          </p>
          <p>
            <strong
              >If you're experiencing dry skin despite moisturizing,</strong
            >
            you might also need a little extra boost. For extremely dry skin,
            consider layering an
            <a
              href="https://catham.eu/products/natural-retinol-alternative-oil-serum"
              class="inline-link"
              ><strong>oil serum</strong></a
            >
            underneath your moisturizer. The oil serum helps lock in moisture
            and prevents further dehydration, creating a richer, more protective
            layer over your skin.
          </p>
          <p>
            Look for <strong>Hyaluronic Acid</strong> in your moisturizer for
            added hydration, but remember, adding a nourishing oil serum can
            take things to the next level if your skin is especially parched.
          </p>
        </div>
        <div class="magazine-grid-layout">
          <div class="magazine-grid-item wide">
            <img
              src="https://cdn.shopify.com/s/files/1/0909/6560/6735/files/dry_and_dull_skin.jpg?v=1737652907"
              alt="Dry skin needing hydration"
              class="magazine-image"
            />
            <div class="magazine-overlay-text">
              <span class="overlay-number">04</span>
              <span class="overlay-title">Deep Hydration</span>
              <p>
                Essential ceramides rebuild your skin's natural defense system
              </p>
            </div>
          </div>
        </div>
        <div class="product-showcase">
          <h3 class="showcase-title">Try This</h3>
          <div class="product-links">
            <a
              href="https://catham.eu/products/ceramide-hydrating-night-cream"
              class="product-link"
              >Ceramide Hydrating Night Cream</a
            >
            <a
              href="https://catham.eu/products/natural-retinol-alternative-oil-serum"
              class="product-link"
              >Firming Bakuchiol Oil Serum</a
            >
          </div>
        </div>
        <h2 class="section-heading">
          5. Your Skin Has Micro Inflammation, Redness and Puffiness (Broken
          Barrier)
        </h2>
        <div class="editorial-text">
          <p>
            When your skin barrier is compromised, you'll notice persistent
            redness, sensitivity, and that puffy, inflamed look that makeup
            can't quite cover. This is your skin crying out for barrier repair
            and anti-inflammatory support.
          </p>
          <p>
            A damaged skin barrier allows irritants in and moisture out,
            creating a cycle of inflammation. Look for products with
            <strong>ceramides</strong> to rebuild the barrier,
            <strong>niacinamide</strong> to calm inflammation, and rich,
            protective formulas that create a healing environment for your skin.
          </p>
        </div>
        <div class="magazine-grid-layout">
          <div class="magazine-grid-item wide">
            <img
              src="https://cdn.shopify.com/s/files/1/0909/6560/6735/files/inflamed_skin.jpg?v=1749204063"
              alt="Inflamed skin with redness and sensitivity"
              class="magazine-image"
            />
            <div class="magazine-overlay-text">
              <span class="overlay-number">05</span>
              <span class="overlay-title">Barrier Repair</span>
              <p>
                Healing begins when you give your barrier what it needs to
                repair itself
              </p>
            </div>
          </div>
        </div>
        <div class="product-showcase">
          <h3 class="showcase-title">Try This</h3>
          <div class="product-links">
            <a
              class="product-link"
              href="https://catham.eu/products/ceramide-hydrating-night-cream"
              >Ceramide Hydrating Night Cream</a
            >
            <a
              class="product-link"
              href="https://catham.eu/products/niacinamide-gel-moisturiser"
              >Niacinamide 5% Gel Moisturiser</a
            >
            <a
              class="product-link"
              href="https://catham.eu/products/rich-collagen-protection-cream"
              >Rich Collagen Protection Cream</a
            >
          </div>
        </div>
        <div class="conclusion-section">
          <h2 class="section-heading centered">The Bottom Line</h2>
          <div class="editorial-text">
            <p>
              Your skin is constantly evolving, and your skincare routine should
              evolve with it. If you're experiencing any of these five signs,
              it's not a failure—it's simply time to upgrade your approach. The
              key is choosing products with proven ingredients that address your
              specific concerns, rather than hoping generic solutions will
              deliver targeted results.
            </p>
            <p>
              Remember: aging gracefully isn't about stopping time. It's about
              giving your skin exactly what it needs to thrive at every stage of
              life.
            </p>
          </div>
        </div>

        <!-- Hero Image at End -->
        <div class="magazine-image-feature">
          <div class="magazine-image-container">
            <img
              src="https://cdn.shopify.com/s/files/1/0909/6560/6735/files/catham_organic_skincare.jpg?v=1748162778"
              alt="Aging gracefully with effective skincare"
              class="magazine-image"
            />
            <div class="magazine-overlay">
              <div class="magazine-tagline">
                Aging is inevitable, but settling for tired skin isn't
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</article>
<style>
  /* Magazine Editorial Styling - Scoped to avoid conflicts */
  .editorial-article * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  .editorial-article {
    font-family: Helvetica, Arial, sans-serif;
    font-size: 11px;
    line-height: 1.65;
    color: #1a1a1a;
    background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
    margin: 0;
    padding: 0;
  }

  .editorial-article {
    max-width: 800px;
    margin: 0 auto;
    padding: 80px 50px;
    background-color: #ffffff;
    box-shadow: 0 0 60px rgba(0, 0, 0, 0.08);
    position: relative;
    font-family: Helvetica, Arial, sans-serif;
    font-size: 11px;
    line-height: 1.65;
    color: #1a1a1a;
  }

  .editorial-article::before {
    content: "";
    position: absolute;
    top: 0;
    left: 50px;
    right: 50px;
    height: 1px;
    background: linear-gradient(
      90deg,
      transparent 0%,
      #2c2c2c 50%,
      transparent 100%
    );
  }

  /* Magazine Header Styling */
  .article-header {
    text-align: center;
    margin-bottom: 60px;
    padding-bottom: 40px;
    position: relative;
    background-color: transparent;
    color: #1a1a1a;
    padding-top: 0;
  }

  .article-header::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 2px;
    background: linear-gradient(
      90deg,
      transparent 0%,
      #1a1a1a 50%,
      transparent 100%
    );
  }

  .header-meta {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 30px;
    font-family: "Fira Code", monospace;
    font-size: 12px;
    letter-spacing: 2px;
    font-weight: 500;
    color: #666;
  }

  .article-title {
    font-family: "Old Standard TT", serif;
    font-size: 3.2rem;
    font-weight: 700;
    line-height: 0.95;
    margin-bottom: 25px;
    letter-spacing: -0.03em;
    text-transform: uppercase;
    position: relative;
    color: #000000;
  }

  .article-title::before {
    content: "";
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: #2c2c2c;
  }

  .article-subtitle {
    font-family: "Old Standard TT", serif;
    font-size: 1.4rem;
    font-style: italic;
    margin-bottom: 40px;
    color: #666;
    letter-spacing: 0.08em;
    text-transform: lowercase;
  }

  .byline {
    display: flex;
    justify-content: center;
    gap: 20px;
    font-family: "Fira Code", monospace;
    font-size: 14px;
    color: #666;
  }

  /* Hero Section */
  .hero-section {
    position: relative;
    height: 60vh;
    overflow: hidden;
  }

  .hero-image-container {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
  }

  .image-caption {
    position: absolute;
    bottom: 30px;
    left: 40px;
    background-color: rgba(0, 0, 0, 0.8);
    color: #fff;
    padding: 15px 25px;
    font-size: 14px;
    font-style: italic;
    letter-spacing: 1px;
  }

  .image-caption-bottom {
    text-align: center;
    margin-top: 15px;
    font-size: 14px;
    font-style: italic;
    color: #666;
    letter-spacing: 1px;
  }

  /* Magazine Grid Layouts */
  .magazine-grid-layout {
    margin: 60px 0;
    position: relative;
  }

  .magazine-grid-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    align-items: start;
  }

  .magazine-grid-item {
    position: relative;
    overflow: hidden;
  }

  .magazine-grid-item.large {
    width: 100%;
    height: 400px;
    border: 8px solid #ffffff;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }

  .magazine-grid-item.medium {
    height: 300px;
    border: 6px solid #ffffff;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  }

  .magazine-grid-item.small {
    background-color: #f8f8f8;
    padding: 30px;
    border: 2px solid #e8e8e8;
  }

  .magazine-grid-item.wide {
    width: 100%;
    height: 350px;
    border: 8px solid #ffffff;
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.18);
    position: relative;
  }

  .magazine-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.4s ease;
  }

  .magazine-image:hover {
    transform: scale(1.05);
  }

  .magazine-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent 0%, rgba(0, 0, 0, 0.9) 100%);
    padding: 40px 25px 20px;
    color: white;
  }

  .caption-number {
    font-family: "Old Standard TT", serif;
    font-size: 2rem;
    font-weight: 700;
    color: #ffffff;
    display: block;
    margin-bottom: 8px;
  }

  .caption-text {
    font-family: Helvetica, Arial, sans-serif;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.9);
    letter-spacing: 0.5px;
    text-transform: uppercase;
  }

  .magazine-text-block {
    padding: 20px 0;
  }

  .magazine-text-block h4 {
    font-family: "Fira Code", monospace;
    font-size: 1.2rem;
    font-weight: 600;
    color: #000000;
    margin-bottom: 15px;
    text-transform: none;
    letter-spacing: 0.5px;
  }

  .magazine-text-block p {
    font-family: Helvetica, Arial, sans-serif;
    font-size: 11px;
    line-height: 1.6;
    color: #666666;
    margin: 0;
  }

  .magazine-overlay-text {
    position: absolute;
    top: 30px;
    left: 30px;
    background: rgba(255, 255, 255, 0.95);
    padding: 25px 30px;
    max-width: 250px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  .overlay-number {
    font-family: "Old Standard TT", serif;
    font-size: 1.8rem;
    font-weight: 700;
    color: #000000;
    display: block;
    margin-bottom: 8px;
  }

  .overlay-title {
    font-family: "Fira Code", monospace;
    font-size: 1rem;
    font-weight: 600;
    color: #000000;
    display: block;
    margin-bottom: 10px;
    text-transform: none;
    letter-spacing: 0.5px;
  }

  .magazine-overlay-text p {
    font-family: Helvetica, Arial, sans-serif;
    font-size: 10px;
    line-height: 1.5;
    color: #333333;
    margin: 0;
  }

  /* Magazine Style Image Feature */
  .magazine-image-feature {
    margin: 60px 0 40px;
    text-align: center;
  }

  .magazine-image-container {
    position: relative;
    display: inline-block;
    max-width: 500px;
    width: 100%;
  }

  .magazine-tagline {
    font-family: "Old Standard TT", serif;
    font-size: 1.3rem;
    font-style: italic;
    color: #ffffff;
    line-height: 1.4;
    letter-spacing: 0.02em;
    text-align: center;
    margin: 0;
  }

  /* Magazine Content */
  .editorial-content {
    padding: 0;
    max-width: 100%;
    margin: 0 auto;
  }

  .lead-paragraph {
    font-size: 1.1rem;
    line-height: 1.7;
    margin-bottom: 40px;
    color: #333333;
    font-weight: 400;
    text-align: left;
    font-family: Helvetica, Arial, sans-serif;
  }

  .lead-paragraph:first-letter {
    font-family: "Old Standard TT", serif;
    font-size: 4rem;
    font-weight: 700;
    line-height: 1;
    float: left;
    margin: 8px 8px 0 0;
    color: #1a1a1a;
  }

  .content-grid {
    max-width: 100%;
    margin: 0 auto;
  }

  .main-content {
    max-width: none;
  }

  .editorial-text {
    font-family: Helvetica, Arial, sans-serif;
    font-size: 11px;
    margin-bottom: 25px;
    line-height: 1.7;
    max-width: 100%;
    color: #333333;
    text-align: left;
  }

  .section-heading {
    font-family: "Old Standard TT", serif;
    font-size: 2.4rem;
    font-weight: 700;
    color: #000000;
    margin: 80px 0 40px;
    letter-spacing: -0.02em;
    line-height: 1.1;
    position: relative;
    padding-left: 30px;
  }

  .section-heading::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(180deg, #2c2c2c 0%, #1a1a1a 100%);
  }

  /* Magazine Product Showcase */
  .product-showcase {
    background-color: #f8f8f8;
    padding: 30px;
    margin: 30px 0;
    border-left: 4px solid #000000;
    border-radius: 4px;
  }

  .showcase-title {
    font-family: "Old Standard TT", serif;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 30px;
    color: #000000;
    letter-spacing: 0.02em;
  }

  .product-grid {
    display: flex;
    flex-direction: column;
    gap: 30px;
    align-items: flex-start;
  }

  .product-item {
    display: flex;
    align-items: center;
    gap: 20px;
    width: 100%;
  }

  .product-image {
    width: 150px;
    height: auto;
    flex-shrink: 0;
  }

  .product-links {
    display: flex;
    flex-direction: column;
    gap: 15px;
    flex: 1;
  }

  .product-link {
    color: #000;
    text-decoration: underline;
    font-weight: 600;
    font-size: 16px;
    transition: opacity 0.3s ease;
  }

  .product-link:hover {
    opacity: 0.7;
  }

  .inline-link {
    color: #000;
    text-decoration: underline;
    font-weight: 600;
    transition: opacity 0.3s ease;
  }

  .inline-link:hover {
    opacity: 0.7;
  }

  /* Magazine Image Features */
  .image-feature {
    margin: 60px 0;
    position: relative;
    overflow: hidden;
  }

  .feature-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
    object-position: center;
    border-radius: 0;
    box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);
    transition: transform 0.3s ease;
  }

  .feature-image:hover {
    transform: scale(1.02);
  }

  /* Magazine Image with Text Overlay */
  .magazine-image-with-text {
    position: relative;
    margin: 60px 0;
    height: 500px;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 12px 50px rgba(0, 0, 0, 0.15);
  }

  .magazine-image-with-text img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
  }

  .magazine-text-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent 0%, rgba(0, 0, 0, 0.8) 100%);
    padding: 60px 40px 40px;
    color: white;
  }

  .magazine-text-overlay h3 {
    font-family: "Old Standard TT", serif;
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 15px;
    color: white;
  }

  .magazine-text-overlay p {
    font-size: 14px;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
  }

  /* Magazine Conclusion Section */
  .conclusion-section {
    margin-top: 80px;
    padding-top: 40px;
    border-top: 2px solid #e8e8e8;
    text-align: center;
  }

  .section-heading.centered {
    text-align: center;
    margin-bottom: 40px;
    padding-left: 0;
  }

  .section-heading.centered::before {
    display: none;
  }

  /* Responsive Design */

  @media (max-width: 768px) {
    .editorial-article {
      padding: 40px 20px;
    }

    .article-title {
      font-size: 2rem;
    }

    .section-heading {
      font-size: 1.6rem;
      padding-left: 20px;
    }

    .product-showcase {
      padding: 20px;
    }

    .lead-paragraph {
      font-size: 1rem;
    }

    .editorial-text {
      font-size: 11px;
    }

    .magazine-image-container {
      max-width: 350px;
    }

    .magazine-overlay {
      top: 20px;
      left: 20px;
      right: 20px;
      padding: 20px 25px;
    }

    .magazine-tagline {
      font-size: 1.1rem;
    }

    .magazine-grid-container {
      grid-template-columns: 1fr;
      gap: 20px;
    }

    .magazine-grid-item.large,
    .magazine-grid-item.medium,
    .magazine-grid-item.wide {
      height: 250px;
      border-width: 4px;
    }

    .magazine-overlay-text {
      top: 20px;
      left: 20px;
      padding: 20px;
      max-width: 200px;
    }

    .overlay-number {
      font-size: 1.4rem;
    }

    .overlay-title {
      font-size: 1rem;
    }

    .caption-number {
      font-size: 1.6rem;
    }

    .caption-text {
      font-size: 11px;
    }
  }

  @media (max-width: 480px) {
    .article-title {
      font-size: 1.8rem;
    }

    .section-heading {
      font-size: 1.4rem;
    }

    body {
      font-size: 15px;
    }
  }
</style>

<script>
  // Detect user location and update product names/links for US users
  function updateProductsForUSUsers() {
    // Simple IP-based detection (you can replace with more sophisticated geolocation)
    fetch("https://ipapi.co/json/")
      .then((response) => response.json())
      .then((data) => {
        if (data.country_code === "US") {
          // Update Rich Collagen Protection Cream to Rich Barrier Cream
          const collagenLinks = document.querySelectorAll(
            'a[href*="rich-collagen-protection-cream"]'
          );
          collagenLinks.forEach((link) => {
            link.href = "https://catham.eu/products/rich-barrier-cream";
            link.textContent = "Rich Barrier Cream";
          });

          // Update Collagen Lifting Serum to Face Elasticity Serum
          const liftingLinks = document.querySelectorAll(
            'a[href*="collagen-lifting-serum"]'
          );
          liftingLinks.forEach((link) => {
            link.href = "https://catham.eu/products/face-elasticity-serum";
            link.textContent = "Face Elasticity Serum";
          });
        }
      })
      .catch((error) => {
        console.log(
          "Geolocation detection failed, using default product names"
        );
      });
  }

  // Run the function when the page loads
  document.addEventListener("DOMContentLoaded", updateProductsForUSUsers);
</script>

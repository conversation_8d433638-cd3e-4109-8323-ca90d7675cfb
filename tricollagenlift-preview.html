<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cathám Tri-CollagenLift™ - Editorial Layout</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Inter', sans-serif;
            background: #f8f8f8;
        }
    </style>
</head>
<body>
    <!-- Include the TricollagenLift section -->
    <div id="tricollagenlift-section"></div>

    <script>
        // Load the TricollagenLift section content
        fetch('tricollagenlift collection section.html')
            .then(response => response.text())
            .then(html => {
                document.getElementById('tricollagenlift-section').innerHTML = html;
            })
            .catch(error => {
                console.error('Error loading content:', error);
                // Fallback content
                document.getElementById('tricollagenlift-section').innerHTML = `
                    <div style="padding: 40px; text-align: center;">
                        <h2>Loading Cathám Tri-CollagenLift™ Editorial...</h2>
                        <p>Please ensure both files are in the same directory.</p>
                    </div>
                `;
            });
    </script>
</body>
</html>

<!-- manifesto-section.html	 -->
<div class="editorial-magazine">
  <!-- Magazine Header -->
  <header class="magazine-masthead">
    <div class="issue-details">
      <span class="issue-number">N°01</span>
      <span class="issue-date">HIGH PERFORMANCE SKINCARE 2025</span>
    </div>
    <h1 class="magazine-title">TRI-COLLAGENLIFT™</h1>
    <div class="magazine-subtitle">THE ARCHITECTURE OF RENEWAL</div>
  </header>
  <!-- Hero Section with Side Texts -->
  <div class="hero-section">
    <div class="hero-container">
      <div class="hero-side-text left-text">
        <div class="side-text-content">
          <div class="side-text-line">TRI-COLLAGENLIFT™</div>
          <div class="side-text-line">SYSTEM</div>
        </div>
      </div>
      <div class="hero-image-container">
        <img
          class="hero-image"
          alt="Collagen boosting skincare"
          src="https://cdn.shopify.com/s/files/1/0909/6560/6735/files/skinlift_with_collagen_boosting_skincare_4_catham.jpg?v=1748774989"
        />
      </div>
      <div class="hero-side-text right-text">
        <div class="side-text-content">
          <div class="side-text-line">NO NEEDLES</div>
          <div class="side-text-line">JUST COLLAGEN</div>
          <div class="side-text-line">SCIENCE</div>
        </div>
      </div>
    </div>
  </div>
  <!-- Editorial Introduction -->
  <section class="editorial-section intro-section">
    <div class="section-grid">
      <div class="grid-column sidebar">
        <div class="sidebar-content">
          <div class="sidebar-number">01</div>
          <div class="sidebar-title">THE BREAKTHROUGH</div>
          <div class="sidebar-line"><br /></div>
        </div>
      </div>
      <div class="grid-column main-content">
        <p class="editorial-lead">
          Dermatological research reveals that skin firmness requires more than
          just collagen stimulation.
        </p>
        <p class="editorial-text">
          Our Tri-CollagenLift™ System addresses skin architecture
          comprehensively through three synergistic pathways, utilizing high
          performance actives to transform skin from within. This isn't simply
          about adding collagen—it's about rebuilding the entire structural
          matrix that supports your skin's natural beauty.
        </p>
        <div class="editorial-quote">
          A study found that our skin makes new collagen at a slow rate—only
          about 0.076% per hour. At that pace, visible improvement doesn’t
          happen overnight. Collagen builds gradually, layer by layer, which is
          why consistency is key. Without regular support, the skin struggles to
          keep up with natural loss and environmental stressors. Supporting your
          skin daily helps maintain its strength, resilience, and youthful
          structure over time.
        </div>
        <p class="editorial-citation">
          Am J Physiol. 1998 Apr;274(4):E586–91. “Measurement of dermal collagen
          synthesis rate in vivo in humans” DOI: 10.1152/ajpendo.1998.274.4.E586
        </p>
        <a
          class="editorial-link"
          href="https://pmc.ncbi.nlm.nih.gov/articles/PMC11995770/"
          target="_blank"
          >View Latest Clinical Research</a
        >
      </div>
    </div>
  </section>
  <!-- The Matrix Section - Dark Background -->
  <section class="editorial-section matrix-section dark">
    <div class="section-header">
      <h2 class="section-title">THE TRI-COLLAGENLIFT™ MATRIX</h2>
      <div class="section-line"><br /></div>
    </div>
    <div class="matrix-grid">
      <div class="matrix-column">
        <div class="matrix-phase">
          <div class="phase-number">N°1</div>
          <h3 class="phase-title">ACTIVATE</h3>
          <p class="phase-description">
            Collagen renewal begins here. Cellular signaling is reawakened,
            stimulating fibroblasts to rebuild the skin's structural matrix and
            improve tone, texture, and elasticity.
          </p>
        </div>
      </div>
      <div class="matrix-column">
        <div class="matrix-phase">
          <div class="phase-number">N°2</div>
          <h3 class="phase-title">PROTECT</h3>
          <p class="phase-description">
            New collagen is vulnerable. Here, the skin forms a protective
            barrier against UV rays and environmental stressors, reducing
            collagen degradation and strengthening long-term skin resilience.
          </p>
        </div>
      </div>
      <div class="matrix-column">
        <div class="matrix-phase">
          <div class="phase-number">N°3</div>
          <h3 class="phase-title">BALANCE</h3>
          <p class="phase-description">
            Inflammation disrupts collagen health. This phase calms reactive
            skin, stabilizes the skin barrier, and maintains optimal conditions
            for collagen to function effectively.
          </p>
        </div>
      </div>
    </div>
  </section>
  <!-- Active Complex Section -->
  <section class="editorial-section complex-section">
    <div class="section-header centered">
      <h2 class="section-title">main ingredients we use</h2>
      <div class="section-line"><br /></div>
    </div>
    <div class="complex-grid">
      <div class="complex-item">
        <div class="complex-number">01</div>
        <h4 class="complex-name">Hyaluronic Acid</h4>
        <p class="complex-description">
          Recent research shows hyaluronic acid activates specific cellular
          pathways that stimulate collagen synthesis.
        </p>
      </div>
      <div class="complex-item">
        <div class="complex-number">02</div>
        <h4 class="complex-name">Aloe Juice</h4>
        <p class="complex-description">
          Rich in polysaccharides that soothe inflammation while providing deep
          hydration and supporting skin repair.
        </p>
      </div>
      <div class="complex-item">
        <div class="complex-number">03</div>
        <h4 class="complex-name">Marine Actives</h4>
        <p class="complex-description">
          Derived from Nordic algae, these compounds help maintain skin
          elasticity and resilience.
        </p>
      </div>
      <div class="complex-item">
        <div class="complex-number">04</div>
        <h4 class="complex-name">Vitamin C</h4>
        <p class="complex-description">
          Stabilized form that stimulates collagen synthesis while brightening
          skin and providing powerful antioxidant protection.
        </p>
      </div>
      <div class="complex-item">
        <div class="complex-number">05</div>
        <h4 class="complex-name">Bakuchiol</h4>
        <p class="complex-description">
          Plant-derived retinol alternative that stimulates collagen renewal
          without irritation.
        </p>
      </div>
      <div class="complex-item">
        <div class="complex-number">06</div>
        <h4 class="complex-name">Nordic Berry Blend</h4>
        <p class="complex-description">
          Concentrated antioxidants from cloudberry, lingonberry and sea
          buckthorn that shield collagen from environmental damage.
        </p>
      </div>
      <div class="complex-item">
        <div class="complex-number">07</div>
        <h4 class="complex-name">Ceramide Complex</h4>
        <p class="complex-description">
          Strengthens skin barrier function and supports optimal moisture
          retention for enhanced firmness.
        </p>
      </div>
      <div class="complex-item">
        <div class="complex-number">08</div>
        <h4 class="complex-name">Niacimide</h4>
        <p class="complex-description">
          Niacinamide boosts collagen production by supporting the skin’s
          natural repair processes. It helps strengthen the skin barrier,
          reducing moisture loss and inflammation—both of which can degrade
          collagen over time. 
        </p>
      </div>
      <div class="complex-item">
        <div class="complex-number">09</div>
        <h4 class="complex-name">Botanical Oils &amp; Butters</h4>
        <p class="complex-description">
          <span
            >Essential plant oils and butters including Rosehip, Jojoba, Argan,
            Shea Butter etc. that create the protective barrier foundation
            required for collagen synthesis. These oils prevent inflammation and
            moisture loss that disrupts collagen production, ensuring optimal
            conditions for natural renewal.</span
          >
        </p>
      </div>
    </div>
  </section>
  <!-- Realistic Promise Section -->
  <section class="editorial-section promise-section">
    <div class="promise-content">
      <div class="promise-header">
        <h2 class="promise-title">THE CATHAM PROMISE</h2>
        <div class="promise-line"><br /></div>
      </div>
      <div class="promise-text">
        <p class="promise-lead"> </p>
        <p>
          At Cathám, we believe in honest results, not quick fixes.
          <br /><br />Our Tri-CollagenLift™ System doesn’t mimic injectables—it
          supports your skin’s natural collagen renewal from within.
          <br /><br />By working with your body, not against it, we promote real
          regeneration—restoring firmness, elasticity, and radiance the natural
          way.<br /><br />Collagen building results are individual and depend on
          factors like age, lifestyle, and consistency of use. While results
          take time, they are real—rooted in your skin’s own intelligent repair
          system.
        </p>
      </div>
      <div class="promise-signature">
        <div class="signature-line"><br /></div>
        <div class="signature-name">Cathám</div>
        <div class="signature-title">CLEAN BEAUTY REVOLUTION</div>
      </div>
    </div>
  </section>
</div>
<style>
  /* High-End Editorial Magazine Styling */
  @import url("https://fonts.googleapis.com/css2?family=Old+Standard+TT:ital,wght@0,400;0,700;1,400&family=Fira+Code:wght@300;400;500;600;700&display=swap");

  body {
    margin: 0;
    padding: 0;
    color: #111;
    background-color: #fff;
    font-family: "Fira Code", monospace;
    font-size: 12px;
    line-height: 1.6;
  }

  .editorial-magazine {
    max-width: 100%;
    margin: 0 auto;
    overflow: hidden;
  }

  /* Magazine Masthead */
  .magazine-masthead {
    padding: 60px 40px;
    text-align: center;
    background-color: #000;
    color: #fff;
    position: relative;
  }

  .issue-details {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 20px;
    font-family: "Fira Code", monospace;
    font-size: 12px;
    letter-spacing: 2px;
  }

  .magazine-title {
    font-family: "Old Standard TT", serif;
    font-size: 72px;
    margin: 0 0 20px;
    font-weight: 700;
    letter-spacing: 4px;
    line-height: 1;
    color: #fff;
  }

  .magazine-subtitle {
    font-family: "Fira Code", monospace;
    font-size: 14px;
    letter-spacing: 3px;
    font-weight: 700;
  }

  /* Hero Section */
  .hero-section {
    padding: 60px 20px;
    background-color: #000;
  }

  .hero-container {
    display: flex;
    align-items: center;
    justify-content: center;
    max-width: 1400px;
    margin: 0 auto;
    gap: 20px;
  }

  .hero-side-text {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
  }

  .side-text-content {
    text-align: center;
  }

  .side-text-line {
    font-family: "Old Standard TT", serif;
    font-size: 22px;
    font-weight: 700;
    line-height: 1.2;
    color: #fff;
    margin-bottom: 8px;
    letter-spacing: 2px;
  }

  .hero-image-container {
    flex: 0 0 60%;
    display: flex;
    justify-content: center;
  }

  .hero-image {
    width: 100%;
    height: auto;
    max-width: 100%;
    object-fit: cover;
    border-radius: 8px;
  }

  /* Editorial Sections */
  .editorial-section {
    padding: 100px 40px;
  }

  .editorial-section.dark {
    background-color: #000;
    color: #fff;
  }

  .section-grid {
    display: grid;
    grid-template-columns: 1fr 3fr;
    gap: 60px;
    max-width: 1400px;
    margin: 0 auto;
  }

  .sidebar-content {
    position: sticky;
    top: 100px;
  }

  .sidebar-number {
    font-family: "Fira Code", monospace;
    font-size: 14px;
    margin-bottom: 10px;
  }

  .sidebar-title {
    font-family: "Fira Code", monospace;
    font-size: 14px;
    letter-spacing: 2px;
    margin-bottom: 15px;
    font-weight: 700;
  }

  .sidebar-line {
    width: 40px;
    height: 1px;
    background-color: currentColor;
  }

  .editorial-lead {
    font-size: 28px;
    line-height: 1.4;
    margin-bottom: 30px;
    font-weight: 400;
  }

  .editorial-text {
    font-size: 18px;
    margin-bottom: 30px;
    max-width: 800px;
  }

  .editorial-quote {
    font-size: 18px;
    font-style: italic;
    line-height: 1.4;
    margin: 40px 0 15px;
    padding-left: 30px;
    border-left: 2px solid currentColor;
    max-width: 700px;
  }

  .editorial-citation {
    font-family: "Fira Code", monospace;
    font-size: 12px;
    margin-bottom: 40px;
    padding-left: 30px;
  }

  .editorial-link {
    font-family: "Fira Code", monospace;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: inherit;
    text-decoration: none;
    border-bottom: 1px solid;
    padding-bottom: 2px;
    transition: opacity 0.3s ease;
    font-weight: 700;
  }

  .editorial-link:hover {
    opacity: 0.7;
  }

  /* Section Headers */
  .section-header {
    margin-bottom: 60px;
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
  }

  .section-header.centered {
    text-align: center;
  }

  .section-title {
    font-family: "Old Standard TT", serif;
    font-size: 36px;
    letter-spacing: 2px;
    font-weight: 700;
    margin: 0 0 20px;
    text-transform: uppercase;
  }

  /* Title color adjustments for different backgrounds */
  .editorial-section:not(.dark) .section-title {
    color: #000;
  }

  .editorial-section.dark .section-title {
    color: #fff;
  }

  .section-line {
    width: 60px;
    height: 1px;
    background-color: currentColor;
    margin: 0 0 20px;
  }

  .section-header.centered .section-line {
    margin-left: auto;
    margin-right: auto;
  }

  .section-subtitle {
    font-size: 18px;
    font-style: italic;
    max-width: 600px;
    margin: 0 auto;
  }

  /* Matrix Section */
  .matrix-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    max-width: 1400px;
    margin: 0 auto;
  }

  .matrix-phase {
    padding: 40px;
    background-color: rgba(255, 255, 255, 0.05);
    height: 100%;
  }

  .phase-number {
    font-family: "Old Standard TT", serif;
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 20px;
  }

  .phase-title {
    font-family: "Old Standard TT", serif;
    font-size: 24px;
    letter-spacing: 2px;
    margin: 0 0 30px;
    font-weight: 700;
    color: #fff;
  }

  .phase-description {
    font-size: 14px;
    line-height: 1.6;
  }

  /* Complex Section */
  .complex-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    max-width: 1400px;
    margin: 0 auto;
  }

  .complex-item {
    padding: 30px;
    background-color: #f9f9f9;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .complex-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }

  .complex-number {
    font-family: "Fira Code", monospace;
    font-size: 12px;
    margin-bottom: 15px;
    color: #999;
  }

  .complex-name {
    font-family: "Old Standard TT", serif;
    font-size: 22px;
    margin: 0 0 15px;
    font-weight: 700;
    color: #000;
  }

  .complex-description {
    font-size: 16px;
    line-height: 1.6;
    color: #555;
  }

  /* Promise Section */
  .promise-section {
    background-color: #f9f9f9;
  }

  .promise-content {
    max-width: 800px;
    margin: 0 auto;
  }

  .promise-header {
    text-align: center;
    margin-bottom: 60px;
  }

  .promise-title {
    font-family: "Old Standard TT", serif;
    font-size: 36px;
    letter-spacing: 2px;
    font-weight: 700;
    margin: 0 0 20px;
    text-transform: uppercase;
    color: #000;
  }

  .promise-line {
    width: 60px;
    height: 1px;
    background-color: #000;
    margin: 0 auto;
  }

  .promise-lead {
    font-size: 24px;
    line-height: 1.4;
    margin-bottom: 30px;
  }

  .promise-text p {
    font-size: 18px;
    margin-bottom: 25px;
    line-height: 1.7;
  }

  .promise-signature {
    margin-top: 60px;
    text-align: center;
  }

  .signature-line {
    width: 40px;
    height: 1px;
    background-color: #000;
    margin: 0 auto 20px;
  }

  .signature-name {
    font-size: 24px;
    margin-bottom: 5px;
  }

  .signature-title {
    font-family: "Fira Code", monospace;
    font-size: 10px;
    letter-spacing: 2px;
    font-weight: 700;
  }

  /* Animations */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes growUp {
    from {
      opacity: 0;
      height: 0;
    }
    to {
      opacity: 1;
      height: 100%;
    }
  }

  /* Responsive Adjustments */
  @media (max-width: 1200px) {
    .section-grid {
      grid-template-columns: 1fr 2fr;
    }

    .matrix-grid {
      grid-template-columns: 1fr;
      gap: 30px;
    }

    .timeline-point[data-week="1"] {
      left: 0%;
    }
    .timeline-point[data-week="3"] {
      left: 35%;
    }
    .timeline-point[data-week="8"] {
      left: 70%;
    }

    /* Hero section adjustments for tablets */
    .side-text-line {
      font-size: 20px;
    }
  }

  @media (max-width: 992px) {
    /* Hide side texts on smaller tablets and mobile */
    .hero-container {
      flex-direction: column;
      gap: 0;
    }

    .hero-side-text {
      display: none;
    }

    .hero-image-container {
      flex: 1;
      width: 100%;
    }

    .hero-section {
      padding: 40px 20px;
    }
  }

  @media (max-width: 768px) {
    .magazine-masthead {
      padding: 40px 20px;
    }

    .magazine-title {
      font-size: 48px;
    }

    .editorial-section {
      padding: 60px 20px;
    }

    /* Hide side texts on mobile and make image full width */
    .hero-container {
      flex-direction: column;
      gap: 0;
    }

    .hero-side-text {
      display: none;
    }

    .hero-image-container {
      flex: 1;
      width: 100%;
    }

    .hero-section {
      padding: 40px 20px;
    }

    .section-grid {
      grid-template-columns: 1fr;
      gap: 40px;
    }

    .sidebar-content {
      position: static;
      margin-bottom: 30px;
    }

    .editorial-lead {
      font-size: 24px;
    }

    .editorial-text {
      font-size: 16px;
    }

    .editorial-quote {
      font-size: 20px;
      padding-left: 20px;
    }

    .section-title {
      font-size: 28px;
    }

    .complex-grid {
      grid-template-columns: 1fr;
    }

    .results-timeline {
      padding: 40px 0;
      display: flex;
      flex-direction: column;
      gap: 40px;
    }

    .timeline-track {
      display: none;
    }

    .timeline-point {
      position: static;
      width: 100%;
      opacity: 1;
      transform: none;
      animation: none;
      text-align: center;
      padding: 20px;
      background-color: rgba(255, 255, 255, 0.05);
      border-radius: 8px;
    }

    .point-marker {
      margin: 0 auto 20px;
    }

    .point-result {
      font-size: 36px;
    }

    .point-description {
      max-width: 100%;
      margin: 0 auto;
    }

    .visualization-graph {
      height: 200px;
    }

    .graph-column {
      width: 60px;
    }

    .promise-title {
      font-size: 28px;
    }

    .promise-lead {
      font-size: 20px;
    }

    .promise-text p {
      font-size: 16px;
    }
  }
</style>
<p> </p>
